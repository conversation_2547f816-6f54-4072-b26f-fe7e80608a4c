import React, { useState } from 'react';
import {
  Layout, Typography, Card, Row, Col, Button, Form, Input,
  InputNumber, Select, message, Spin, Divider
} from 'antd';
import {
  RocketOutlined,
  CloudOutlined,
  BookOutlined,
  GithubOutlined,
  SendOutlined
} from '@ant-design/icons';
import './App.css';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

interface TravelPlan {
  destination: string;
  duration: number;
  budget?: number;
  travel_type: string;
  group_size: number;
  special_requirements?: string;
}

function App() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [generatedPlan, setGeneratedPlan] = useState<any>(null);
  const [showForm, setShowForm] = useState(false);

  const handleGeneratePlan = async (values: TravelPlan) => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/travel/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (response.ok) {
        const result = await response.json();
        setGeneratedPlan(result);
        message.success('旅游计划生成成功！');
      } else {
        message.error('生成失败，请重试');
      }
    } catch (error) {
      message.error('网络错误，请检查连接');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout className="layout" style={{ minHeight: '100vh' }}>
      <Header style={{ 
        display: 'flex', 
        alignItems: 'center',
        background: '#001529'
      }}>
        <div style={{ 
          color: 'white', 
          fontSize: '20px', 
          fontWeight: 'bold' 
        }}>
          🌍 OpenTravel
        </div>
      </Header>
      
      <Content style={{ padding: '50px' }}>
        <div style={{ 
          background: '#fff', 
          padding: '24px', 
          minHeight: '280px',
          borderRadius: '8px'
        }}>
          <div style={{ textAlign: 'center', marginBottom: '40px' }}>
            <Title level={1}>
              🤖 OpenTravel AI旅游推荐系统
            </Title>
            <Paragraph style={{ fontSize: '18px', color: '#666' }}>
              基于LangChain的智能旅游推荐平台，集成AI智能规划、实时天气查询和旅游攻略汇总功能
            </Paragraph>

            {!showForm && !generatedPlan && (
              <Button
                type="primary"
                size="large"
                icon={<RocketOutlined />}
                onClick={() => setShowForm(true)}
                style={{ marginTop: '20px' }}
              >
                开始规划旅程
              </Button>
            )}
          </div>

          {/* 旅游计划生成表单 */}
          {showForm && !generatedPlan && (
            <Card title="🎯 创建您的专属旅游计划" style={{ marginBottom: '24px' }}>
              <Form
                form={form}
                layout="vertical"
                onFinish={handleGeneratePlan}
                initialValues={{
                  travel_type: '休闲',
                  group_size: 1,
                }}
              >
                <Row gutter={16}>
                  <Col xs={24} md={12}>
                    <Form.Item
                      label="目的地"
                      name="destination"
                      rules={[{ required: true, message: '请输入目的地' }]}
                    >
                      <Input placeholder="例如：北京、上海、杭州" />
                    </Form.Item>
                  </Col>
                  <Col xs={24} md={6}>
                    <Form.Item
                      label="旅游天数"
                      name="duration"
                      rules={[{ required: true, message: '请输入旅游天数' }]}
                    >
                      <InputNumber min={1} max={30} placeholder="天数" style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col xs={24} md={6}>
                    <Form.Item label="预算（元）" name="budget">
                      <InputNumber min={0} placeholder="可选" style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col xs={24} md={8}>
                    <Form.Item label="旅游类型" name="travel_type">
                      <Select>
                        <Option value="休闲">休闲</Option>
                        <Option value="探险">探险</Option>
                        <Option value="文化">文化</Option>
                        <Option value="美食">美食</Option>
                        <Option value="商务">商务</Option>
                        <Option value="家庭">家庭</Option>
                        <Option value="浪漫">浪漫</Option>
                        <Option value="摄影">摄影</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col xs={24} md={8}>
                    <Form.Item label="出行人数" name="group_size">
                      <InputNumber min={1} max={20} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col xs={24} md={8}>
                    <Form.Item>
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={loading}
                        icon={<SendOutlined />}
                        size="large"
                        style={{ marginTop: '30px', width: '100%' }}
                      >
                        生成旅游计划
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item label="特殊需求" name="special_requirements">
                  <Input.TextArea
                    rows={3}
                    placeholder="例如：无障碍设施、素食、儿童友好等特殊需求"
                  />
                </Form.Item>

                <div style={{ textAlign: 'center' }}>
                  <Button onClick={() => setShowForm(false)}>
                    返回首页
                  </Button>
                </div>
              </Form>
            </Card>
          )}

          {/* 生成的旅游计划 */}
          {generatedPlan && (
            <Card title="🎉 您的专属旅游计划" style={{ marginBottom: '24px' }}>
              <Spin spinning={loading}>
                <div>
                  <Title level={4}>行程概述</Title>
                  <Paragraph>{generatedPlan.plan?.overview || '精心为您定制的旅游计划'}</Paragraph>

                  <Divider />

                  <Title level={4}>详细内容</Title>
                  <div style={{
                    background: '#f5f5f5',
                    padding: '16px',
                    borderRadius: '8px',
                    whiteSpace: 'pre-wrap'
                  }}>
                    <Text>{generatedPlan.raw_response || '计划生成中...'}</Text>
                  </div>

                  <div style={{ marginTop: '16px', textAlign: 'center' }}>
                    <Button
                      type="default"
                      onClick={() => {
                        setGeneratedPlan(null);
                        setShowForm(false);
                        form.resetFields();
                      }}
                      style={{ marginRight: '8px' }}
                    >
                      重新规划
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => setShowForm(true)}
                    >
                      修改计划
                    </Button>
                  </div>
                </div>
              </Spin>
            </Card>
          )}

          {/* 功能特色展示 */}
          {!showForm && !generatedPlan && (
            <Row gutter={[24, 24]} style={{ marginBottom: '40px' }}>
              <Col xs={24} md={8}>
                <Card
                  hoverable
                  style={{ textAlign: 'center', height: '200px' }}
                  bodyStyle={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    height: '100%'
                  }}
                >
                  <RocketOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
                  <Title level={4}>AI智能规划</Title>
                  <Paragraph>基于LangChain框架的智能旅游行程生成</Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={8}>
                <Card
                  hoverable
                  style={{ textAlign: 'center', height: '200px' }}
                  bodyStyle={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    height: '100%'
                  }}
                >
                  <CloudOutlined style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} />
                  <Title level={4}>实时天气</Title>
                  <Paragraph>通过MCP工具集成多源天气数据</Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={8}>
                <Card
                  hoverable
                  style={{ textAlign: 'center', height: '200px' }}
                  bodyStyle={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    height: '100%'
                  }}
                >
                  <BookOutlined style={{ fontSize: '48px', color: '#fa8c16', marginBottom: '16px' }} />
                  <Title level={4}>攻略汇总</Title>
                  <Paragraph>多平台旅游内容聚合和智能分析</Paragraph>
                </Card>
              </Col>
            </Row>
          )}

          {!showForm && !generatedPlan && (
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ marginBottom: '24px' }}>
                🚀 开始您的智能旅游规划
              </Title>
              <Paragraph style={{ fontSize: '16px', marginBottom: '24px' }}>
                使用AI技术为您量身定制完美的旅游计划，让每一次出行都成为难忘的回忆！
              </Paragraph>
              <Button
                type="primary"
                size="large"
                icon={<GithubOutlined />}
                href="https://github.com/makaixindalao/openTravel"
                target="_blank"
                style={{ marginRight: '12px' }}
              >
                查看项目源码
              </Button>
              <Button
                size="large"
                icon={<RocketOutlined />}
                onClick={() => setShowForm(true)}
              >
                立即体验
              </Button>
            </div>
          )}
        </div>
      </Content>
      
      <Footer style={{ textAlign: 'center' }}>
        OpenTravel ©2024 Created with ❤️ by AI Assistant
      </Footer>
    </Layout>
  );
}

export default App;
