#!/bin/bash

# OpenTravel 部署脚本

set -e

echo "🚀 开始部署 OpenTravel AI旅游推荐系统..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f "config/.env" ]; then
    echo "📝 创建环境变量文件..."
    cp config/.env.example config/.env
    echo "⚠️  请编辑 config/.env 文件，填入必要的API密钥"
    echo "   - OPENAI_API_KEY: OpenAI API密钥"
    echo "   - OPENWEATHER_API_KEY: OpenWeather API密钥"
    echo "   - SECRET_KEY: 应用密钥"
    read -p "是否已配置完成？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "请配置完成后重新运行部署脚本"
        exit 1
    fi
fi

# 构建和启动服务
echo "🔨 构建Docker镜像..."
docker-compose build

echo "🗄️ 启动数据库服务..."
docker-compose up -d db redis

echo "⏳ 等待数据库启动..."
sleep 10

echo "📊 初始化数据库..."
docker-compose run --rm backend python scripts/init_db.py

echo "🚀 启动所有服务..."
docker-compose up -d

echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    docker-compose logs backend
    exit 1
fi

if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端服务启动成功"
else
    echo "⚠️  前端服务可能需要更多时间启动"
fi

echo ""
echo "🎉 OpenTravel 部署完成！"
echo ""
echo "📱 访问地址："
echo "   前端应用: http://localhost:3000"
echo "   后端API: http://localhost:8000"
echo "   API文档: http://localhost:8000/docs"
echo ""
echo "🔧 管理命令："
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo ""
echo "📚 更多信息请查看 README.md 文件"
