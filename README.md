# OpenTravel - AI旅游推荐系统

基于LangChain的智能旅游推荐平台，集成AI智能规划、实时天气查询和旅游攻略汇总功能。

## 🌟 项目特色

- **🤖 AI智能规划**: 基于LangChain框架的智能旅游行程生成
- **🌤️ 实时天气**: 通过MCP工具集成多源天气数据
- **📖 攻略汇总**: 多平台旅游内容聚合和智能分析
- **🎯 个性化推荐**: 基于用户偏好的智能推荐算法

## 🏗️ 技术架构

### 后端技术栈
- **框架**: FastAPI
- **AI框架**: LangChain
- **数据库**: PostgreSQL + Redis
- **大语言模型**: OpenAI GPT-4 / Claude

### 前端技术栈
- **框架**: React
- **UI库**: Ant Design
- **地图**: 高德地图 / 百度地图

### MCP工具
- **天气服务**: OpenWeatherMap API集成
- **内容爬取**: 小红书、马蜂窝、携程等平台
- **地图服务**: 地理位置和路线规划

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Node.js 18+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/openTravel.git
cd openTravel
```

2. **后端环境设置**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **前端环境设置**
```bash
cd frontend
npm install
```

4. **环境变量配置**
```bash
cp config/.env.example config/.env
# 编辑 .env 文件，填入必要的API密钥
```

5. **数据库初始化**
```bash
cd backend
python scripts/init_db.py
```

6. **启动服务**

使用Docker Compose（推荐）:
```bash
docker-compose up -d
```

或分别启动:
```bash
# 后端
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 前端
cd frontend
npm start
```

### 访问应用
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 📁 项目结构

```
openTravel/
├── backend/                 # 后端代码
│   ├── app/                # FastAPI应用
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   └── main.py        # 应用入口
│   ├── tests/             # 测试代码
│   └── scripts/           # 脚本文件
├── frontend/               # 前端代码
│   ├── src/               # React源码
│   └── public/            # 静态资源
├── mcp_tools/             # MCP工具
│   ├── weather/           # 天气服务工具
│   └── content_scraper/   # 内容爬取工具
├── docs/                  # 项目文档
├── config/                # 配置文件
├── docker-compose.yml     # Docker编排
└── README.md             # 项目说明
```

## 🔧 配置说明

### 环境变量
在 `config/.env` 文件中配置以下变量：

```env
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/opentravel
REDIS_URL=redis://localhost:6379

# AI模型配置
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# 天气API
OPENWEATHER_API_KEY=your_openweather_api_key

# 其他配置
SECRET_KEY=your_secret_key
DEBUG=true
```

## 📖 API文档

### 核心接口

#### 生成旅游计划
```http
POST /api/travel/generate
Content-Type: application/json

{
  "destination": "北京",
  "duration": 3,
  "budget": 5000,
  "travel_type": "文化",
  "group_size": 2
}
```

#### 获取天气信息
```http
GET /api/weather/北京?days=7
```

#### 获取攻略汇总
```http
GET /api/guides/北京?platform=xiaohongshu&limit=10
```

详细API文档请访问: http://localhost:8000/docs

## 🧪 测试

```bash
# 后端测试
cd backend
pytest

# 前端测试
cd frontend
npm test
```

## 📦 部署

### Docker部署
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### 手动部署
请参考 `docs/deployment.md` 文档

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目链接: https://github.com/your-username/openTravel
- 问题反馈: https://github.com/your-username/openTravel/issues

## 🙏 致谢

- [LangChain](https://github.com/langchain-ai/langchain) - AI应用开发框架
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Python Web框架
- [React](https://reactjs.org/) - 用户界面库
- [Ant Design](https://ant.design/) - 企业级UI设计语言
