# OpenTravel 部署指南

## 概述

本文档详细介绍了如何在不同环境中部署 OpenTravel AI旅游推荐系统。

## 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **操作系统**: Linux (Ubuntu 20.04+), macOS, Windows 10+

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 稳定的互联网连接

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- Git 2.30+

## 快速部署

### 1. 克隆项目
```bash
git clone https://github.com/makaixindalao/openTravel.git
cd openTravel
```

### 2. 配置环境变量
```bash
cp config/.env.example config/.env
```

编辑 `config/.env` 文件，填入必要的配置：
```env
# AI模型配置
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 天气API配置
OPENWEATHER_API_KEY=your_openweather_api_key_here

# 应用配置
SECRET_KEY=your_super_secret_key_here
DEBUG=false
```

### 3. 一键部署
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

### 4. 验证部署
访问以下地址验证部署是否成功：
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 手动部署

### 1. 数据库部署

#### PostgreSQL
```bash
# 使用Docker部署PostgreSQL
docker run -d \
  --name opentravel-postgres \
  -e POSTGRES_DB=opentravel \
  -e POSTGRES_USER=opentravel_user \
  -e POSTGRES_PASSWORD=opentravel_pass \
  -p 5432:5432 \
  -v postgres_data:/var/lib/postgresql/data \
  postgres:15-alpine
```

#### Redis
```bash
# 使用Docker部署Redis
docker run -d \
  --name opentravel-redis \
  -p 6379:6379 \
  -v redis_data:/data \
  redis:7-alpine redis-server --appendonly yes
```

### 2. 后端部署

#### 构建镜像
```bash
cd backend
docker build -t opentravel-backend .
```

#### 运行容器
```bash
docker run -d \
  --name opentravel-backend \
  -p 8000:8000 \
  -e DATABASE_URL=**********************************************************/opentravel \
  -e REDIS_URL=redis://redis:6379 \
  -e OPENAI_API_KEY=your_api_key \
  --link opentravel-postgres:postgres \
  --link opentravel-redis:redis \
  opentravel-backend
```

### 3. 前端部署

#### 构建镜像
```bash
cd frontend
docker build -t opentravel-frontend .
```

#### 运行容器
```bash
docker run -d \
  --name opentravel-frontend \
  -p 3000:3000 \
  -e REACT_APP_API_URL=http://localhost:8000 \
  opentravel-frontend
```

## 生产环境部署

### 1. 使用 Docker Compose (推荐)

创建 `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend

  backend:
    build: ./backend
    environment:
      - DEBUG=false
      - DATABASE_URL=******************************/opentravel
    depends_on:
      - db
      - redis

  frontend:
    build: ./frontend
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=https://api.yourdomain.com

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=opentravel
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

部署命令：
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### 2. Kubernetes 部署

创建 Kubernetes 配置文件：

#### Namespace
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: opentravel
```

#### ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: opentravel-config
  namespace: opentravel
data:
  DATABASE_URL: "************************************/opentravel"
  REDIS_URL: "redis://redis:6379"
```

#### Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: opentravel-backend
  namespace: opentravel
spec:
  replicas: 3
  selector:
    matchLabels:
      app: opentravel-backend
  template:
    metadata:
      labels:
        app: opentravel-backend
    spec:
      containers:
      - name: backend
        image: opentravel-backend:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: opentravel-config
```

### 3. 云服务部署

#### AWS ECS
1. 创建 ECS 集群
2. 定义任务定义
3. 创建服务
4. 配置负载均衡器

#### Google Cloud Run
```bash
# 构建并推送镜像
gcloud builds submit --tag gcr.io/PROJECT_ID/opentravel-backend

# 部署到 Cloud Run
gcloud run deploy opentravel-backend \
  --image gcr.io/PROJECT_ID/opentravel-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

## 监控和日志

### 1. 日志管理
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend

# 查看最近的日志
docker-compose logs --tail=100 backend
```

### 2. 健康检查
```bash
# 检查服务状态
curl http://localhost:8000/health

# 检查系统状态
curl http://localhost:8000/api/v1/status
```

### 3. 性能监控

使用 Prometheus + Grafana 进行监控：

```yaml
# 添加到 docker-compose.yml
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

## 备份和恢复

### 1. 数据库备份
```bash
# 备份数据库
docker exec opentravel-postgres pg_dump -U opentravel_user opentravel > backup.sql

# 恢复数据库
docker exec -i opentravel-postgres psql -U opentravel_user opentravel < backup.sql
```

### 2. 文件备份
```bash
# 备份上传文件
tar -czf uploads_backup.tar.gz uploads/

# 备份配置文件
tar -czf config_backup.tar.gz config/
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接字符串配置
   - 检查网络连接

2. **API 响应慢**
   - 检查数据库查询性能
   - 验证缓存配置
   - 监控系统资源使用

3. **前端无法访问后端**
   - 检查 CORS 配置
   - 验证 API URL 配置
   - 检查网络防火墙设置

### 调试命令
```bash
# 进入容器调试
docker exec -it opentravel-backend bash

# 查看容器资源使用
docker stats

# 检查容器网络
docker network ls
docker network inspect opentravel_default
```

## 安全配置

### 1. HTTPS 配置
```nginx
server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    location / {
        proxy_pass http://frontend:3000;
    }
    
    location /api {
        proxy_pass http://backend:8000;
    }
}
```

### 2. 防火墙配置
```bash
# 只开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

### 3. 环境变量安全
- 使用 Docker secrets 或 Kubernetes secrets
- 定期轮换 API 密钥
- 限制数据库用户权限

## 更新和维护

### 1. 应用更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建和部署
docker-compose build
docker-compose up -d
```

### 2. 数据库迁移
```bash
# 运行数据库迁移
docker-compose run --rm backend python scripts/migrate.py
```

### 3. 定期维护
- 清理未使用的 Docker 镜像
- 备份重要数据
- 更新系统依赖
- 监控日志文件大小

## 支持

如需部署支持，请联系：
- GitHub Issues: https://github.com/makaixindalao/openTravel/issues
- 邮箱: <EMAIL>
