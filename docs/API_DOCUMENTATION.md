# OpenTravel API 文档

## 概述

OpenTravel API 是基于 FastAPI 构建的 RESTful API，为 AI 旅游推荐系统提供后端服务。

## 基础信息

- **Base URL**: `http://localhost:8000`
- **API Version**: v1
- **Content-Type**: `application/json`

## 认证

目前 API 处于开发阶段，大部分接口无需认证。未来将支持 JWT Token 认证。

## 核心接口

### 1. 系统状态

#### 健康检查
```http
GET /health
```

**响应示例**:
```json
{
  "status": "healthy",
  "service": "OpenTravel API",
  "version": "1.0.0"
}
```

#### 系统状态
```http
GET /api/v1/status
```

**响应示例**:
```json
{
  "api_version": "v1",
  "status": "running",
  "components": {
    "database": "healthy",
    "redis": "healthy",
    "ai_service": "configured"
  },
  "features": {
    "ai_planning": "available",
    "weather_integration": "development",
    "guide_aggregation": "development"
  }
}
```

### 2. 旅游计划

#### 生成旅游计划
```http
POST /api/v1/travel/generate
```

**请求体**:
```json
{
  "destination": "北京",
  "duration": 3,
  "budget": 3000,
  "travel_type": "文化",
  "group_size": 2,
  "special_requirements": "素食"
}
```

**响应示例**:
```json
{
  "success": true,
  "plan_id": "uuid-string",
  "plan": {
    "overview": "北京3日文化之旅",
    "daily_itinerary": [...],
    "accommodations": [...],
    "transportation": {...},
    "dining": [...],
    "tips": [...]
  },
  "raw_response": "AI生成的原始文本",
  "metadata": {...}
}
```

#### 获取旅游计划列表
```http
GET /api/v1/travel/plans?page=1&size=10&destination=北京
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 10, 最大: 100)
- `destination`: 目的地筛选 (可选)
- `travel_type`: 旅游类型筛选 (可选)

#### 获取单个旅游计划
```http
GET /api/v1/travel/plans/{plan_id}
```

#### 更新旅游计划
```http
PUT /api/v1/travel/plans/{plan_id}
```

#### 删除旅游计划
```http
DELETE /api/v1/travel/plans/{plan_id}
```

#### 搜索旅游计划
```http
GET /api/v1/travel/search?q=北京&page=1&size=10
```

#### 热门目的地
```http
GET /api/v1/travel/destinations/popular?limit=10
```

### 3. 用户认证 (开发中)

#### 用户注册
```http
POST /api/v1/auth/register
```

#### 用户登录
```http
POST /api/v1/auth/login
```

#### 获取当前用户
```http
GET /api/v1/auth/me
```

## 错误处理

API 使用标准的 HTTP 状态码：

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `404`: 资源不存在
- `500`: 服务器内部错误

**错误响应格式**:
```json
{
  "detail": "错误描述信息"
}
```

## 数据模型

### TravelPlan
```json
{
  "id": "string",
  "destination": "string",
  "duration": "integer",
  "budget": "number",
  "travel_type": "string",
  "group_size": "integer",
  "special_requirements": "string",
  "plan_content": "object",
  "status": "string",
  "created_at": "datetime"
}
```

### TravelPlanContent
```json
{
  "overview": "string",
  "daily_itinerary": "array",
  "accommodations": "array",
  "transportation": "object",
  "dining": "array",
  "shopping": "array",
  "tips": "array",
  "budget_breakdown": "object"
}
```

## 限制和配额

- API 请求频率限制: 60 次/分钟
- 单次请求最大数据量: 10MB
- 旅游计划最大天数: 30天
- 最大出行人数: 20人

## SDK 和工具

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI Schema**: `http://localhost:8000/openapi.json`

## 示例代码

### Python
```python
import requests

# 生成旅游计划
response = requests.post(
    "http://localhost:8000/api/v1/travel/generate",
    json={
        "destination": "北京",
        "duration": 3,
        "travel_type": "文化",
        "group_size": 2
    }
)

if response.status_code == 200:
    plan = response.json()
    print(f"计划生成成功: {plan['plan']['overview']}")
```

### JavaScript
```javascript
// 生成旅游计划
fetch('/api/v1/travel/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    destination: '北京',
    duration: 3,
    travel_type: '文化',
    group_size: 2
  })
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('计划生成成功:', data.plan.overview);
  }
});
```

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础旅游计划生成功能
- 计划管理 CRUD 操作
- 搜索和筛选功能

## 支持

如有问题或建议，请通过以下方式联系：

- GitHub Issues: https://github.com/makaixindalao/openTravel/issues
- 邮箱: <EMAIL>
