# OpenTravel 开发指南

## 开发环境设置

### 前置要求
- Python 3.11+
- Node.js 18+
- PostgreSQL 15+
- Redis 7+
- Git

### 本地开发环境搭建

1. **克隆项目**
```bash
git clone https://github.com/makaixindalao/openTravel.git
cd openTravel
```

2. **后端环境设置**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **前端环境设置**
```bash
cd frontend
npm install
```

4. **环境变量配置**
```bash
cp config/.env.example config/.env
# 编辑 .env 文件，填入必要的配置
```

5. **数据库设置**
```bash
# 启动PostgreSQL和Redis
docker-compose up -d db redis

# 运行数据库迁移
cd backend
python scripts/init_db.py
```

## 开发工作流

### 分支管理
- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成测试
- `feature/*`: 功能分支，用于开发新功能
- `hotfix/*`: 热修复分支，用于紧急修复

### 代码规范
- Python: 使用 Black + isort + flake8
- TypeScript: 使用 ESLint + Prettier
- 提交信息: 使用约定式提交格式

### 测试
```bash
# 后端测试
cd backend
pytest

# 前端测试
cd frontend
npm test
```

## 项目架构

### 后端架构
```
backend/
├── app/
│   ├── api/          # API路由
│   ├── core/         # 核心配置
│   ├── models/       # 数据模型
│   ├── services/     # 业务逻辑
│   └── main.py       # 应用入口
├── tests/            # 测试代码
└── scripts/          # 脚本文件
```

### 前端架构
```
frontend/
├── src/
│   ├── components/   # 组件
│   ├── pages/        # 页面
│   ├── services/     # API服务
│   ├── utils/        # 工具函数
│   └── App.tsx       # 应用入口
└── public/           # 静态资源
```

### MCP工具架构
```
mcp_tools/
├── weather/          # 天气服务工具
└── content_scraper/  # 内容爬取工具
```

## API开发指南

### 路由规范
- `/api/v1/travel/` - 旅游相关接口
- `/api/v1/weather/` - 天气相关接口
- `/api/v1/guides/` - 攻略相关接口
- `/api/v1/users/` - 用户相关接口

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 部署指南

### Docker部署
```bash
# 开发环境
docker-compose up -d

# 生产环境
docker-compose -f docker-compose.prod.yml up -d
```

### 手动部署
参考 `docs/deployment.md` 文档

## 常见问题

### Q: 如何添加新的API接口？
A: 在 `backend/app/api/` 目录下创建对应的路由文件，并在 `main.py` 中注册路由。

### Q: 如何添加新的前端页面？
A: 在 `frontend/src/pages/` 目录下创建页面组件，并在路由配置中添加对应路由。

### Q: 如何开发新的MCP工具？
A: 在 `mcp_tools/` 目录下创建新的工具目录，实现对应的MCP接口。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request
5. 代码审查
6. 合并代码

## 联系方式

- 项目地址: https://github.com/makaixindalao/openTravel
- 问题反馈: https://github.com/makaixindalao/openTravel/issues
