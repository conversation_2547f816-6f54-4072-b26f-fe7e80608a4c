version: '3.8'

services:
  # PostgreSQL数据库
  db:
    image: postgres:15-alpine
    container_name: opentravel_db
    environment:
      POSTGRES_DB: opentravel
      POSTGRES_USER: opentravel_user
      POSTGRES_PASSWORD: opentravel_pass
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - opentravel_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U opentravel_user -d opentravel"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: opentravel_redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - opentravel_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: opentravel_backend
    environment:
      - DATABASE_URL=****************************************************/opentravel
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OPENWEATHER_API_KEY=${OPENWEATHER_API_KEY}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DEBUG=${DEBUG:-false}
    volumes:
      - ./backend:/app
      - ./config:/app/config
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - opentravel_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端Web服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: opentravel_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_ENV=${NODE_ENV:-development}
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - opentravel_network
    restart: unless-stopped

  # Celery Worker (异步任务处理)
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: opentravel_celery_worker
    command: celery -A app.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=****************************************************/opentravel
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OPENWEATHER_API_KEY=${OPENWEATHER_API_KEY}
    volumes:
      - ./backend:/app
      - ./config:/app/config
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - opentravel_network
    restart: unless-stopped

  # Celery Beat (定时任务调度)
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: opentravel_celery_beat
    command: celery -A app.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=****************************************************/opentravel
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./backend:/app
      - ./config:/app/config
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - opentravel_network
    restart: unless-stopped

  # Nginx反向代理 (生产环境)
  nginx:
    image: nginx:alpine
    container_name: opentravel_nginx
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./frontend/build:/usr/share/nginx/html
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    networks:
      - opentravel_network
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  opentravel_network:
    driver: bridge
