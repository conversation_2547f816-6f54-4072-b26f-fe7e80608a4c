"""
旅游API测试
"""

import pytest
from fastapi.testclient import TestClient
from backend.app.main import app

client = TestClient(app)


def test_health_check():
    """测试健康检查接口"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"


def test_api_status():
    """测试API状态接口"""
    response = client.get("/api/v1/status")
    assert response.status_code == 200
    data = response.json()
    assert "api_version" in data
    assert "features" in data


def test_generate_travel_plan():
    """测试生成旅游计划接口"""
    plan_data = {
        "destination": "北京",
        "duration": 3,
        "budget": 3000,
        "travel_type": "文化",
        "group_size": 2
    }
    
    response = client.post("/api/v1/travel/generate", json=plan_data)
    # 由于需要AI服务，这里可能会失败，但至少验证接口存在
    assert response.status_code in [200, 500]


def test_get_travel_plans():
    """测试获取旅游计划列表接口"""
    response = client.get("/api/v1/travel/plans")
    assert response.status_code == 200
    data = response.json()
    assert "plans" in data
    assert "total" in data


def test_search_travel_plans():
    """测试搜索旅游计划接口"""
    response = client.get("/api/v1/travel/search?q=北京")
    assert response.status_code == 200
    data = response.json()
    assert "plans" in data
    assert "query" in data


def test_popular_destinations():
    """测试热门目的地接口"""
    response = client.get("/api/v1/travel/destinations/popular")
    assert response.status_code == 200
    data = response.json()
    assert "destinations" in data


if __name__ == "__main__":
    pytest.main([__file__])
