-- OpenTravel 数据库初始化SQL脚本

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar_url VARCHAR(500),
    bio TEXT,
    preferences JSONB,
    travel_history JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE
);

-- 创建旅游计划表
CREATE TABLE IF NOT EXISTS travel_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    destination VARCHAR(100) NOT NULL,
    duration INTEGER NOT NULL,
    budget DECIMAL(10,2),
    travel_type VARCHAR(20) NOT NULL DEFAULT '休闲',
    group_size INTEGER NOT NULL DEFAULT 1,
    special_requirements TEXT,
    start_date TIMESTAMP WITH TIME ZONE,
    plan_content JSONB,
    raw_ai_response TEXT,
    weather_info JSONB,
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    is_public BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 创建旅游攻略表
CREATE TABLE IF NOT EXISTS travel_guides (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    platform VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    author VARCHAR(100),
    author_url VARCHAR(500),
    rating DECIMAL(3,2),
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    tags JSONB,
    location VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    url VARCHAR(500),
    images JSONB,
    extracted_info JSONB,
    sentiment_score DECIMAL(3,2),
    published_at TIMESTAMP WITH TIME ZONE,
    scraped_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 创建天气数据表
CREATE TABLE IF NOT EXISTS weather_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    location VARCHAR(100) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    current_temp DECIMAL(5,2),
    feels_like DECIMAL(5,2),
    humidity INTEGER,
    pressure DECIMAL(7,2),
    wind_speed DECIMAL(5,2),
    wind_direction INTEGER,
    visibility DECIMAL(5,2),
    uv_index DECIMAL(3,1),
    weather_main VARCHAR(50),
    weather_description VARCHAR(100),
    weather_icon VARCHAR(10),
    forecast_data JSONB,
    source VARCHAR(50) NOT NULL DEFAULT 'openweather',
    observation_time TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建计划评价表
CREATE TABLE IF NOT EXISTS plan_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    plan_id UUID NOT NULL REFERENCES travel_plans(id),
    user_id UUID NOT NULL REFERENCES users(id),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(100),
    content TEXT,
    accuracy_rating INTEGER CHECK (accuracy_rating >= 1 AND accuracy_rating <= 5),
    practicality_rating INTEGER CHECK (practicality_rating >= 1 AND practicality_rating <= 5),
    creativity_rating INTEGER CHECK (creativity_rating >= 1 AND creativity_rating <= 5),
    is_verified BOOLEAN DEFAULT FALSE,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 创建搜索日志表
CREATE TABLE IF NOT EXISTS search_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    query VARCHAR(200) NOT NULL,
    destination VARCHAR(100),
    filters JSONB,
    result_count INTEGER DEFAULT 0,
    clicked_result_id UUID,
    ip_address INET,
    user_agent VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_travel_plans_user_id ON travel_plans(user_id);
CREATE INDEX IF NOT EXISTS idx_travel_plans_destination ON travel_plans(destination);
CREATE INDEX IF NOT EXISTS idx_travel_plans_destination_duration ON travel_plans(destination, duration);
CREATE INDEX IF NOT EXISTS idx_travel_plans_created_at ON travel_plans(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_travel_plans_status ON travel_plans(status);

CREATE INDEX IF NOT EXISTS idx_travel_guides_platform ON travel_guides(platform);
CREATE INDEX IF NOT EXISTS idx_travel_guides_location ON travel_guides(location);
CREATE INDEX IF NOT EXISTS idx_travel_guides_location_platform ON travel_guides(location, platform);
CREATE INDEX IF NOT EXISTS idx_travel_guides_scraped_at ON travel_guides(scraped_at DESC);

CREATE INDEX IF NOT EXISTS idx_weather_data_location ON weather_data(location);
CREATE INDEX IF NOT EXISTS idx_weather_data_location_time ON weather_data(location, observation_time DESC);
CREATE INDEX IF NOT EXISTS idx_weather_data_observation_time ON weather_data(observation_time DESC);

CREATE INDEX IF NOT EXISTS idx_plan_reviews_plan_id ON plan_reviews(plan_id);
CREATE INDEX IF NOT EXISTS idx_plan_reviews_user_id ON plan_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_plan_reviews_plan_rating ON plan_reviews(plan_id, rating DESC);

CREATE INDEX IF NOT EXISTS idx_search_logs_query ON search_logs(query);
CREATE INDEX IF NOT EXISTS idx_search_logs_destination ON search_logs(destination);
CREATE INDEX IF NOT EXISTS idx_search_logs_created_at ON search_logs(created_at DESC);

-- 创建全文搜索索引
CREATE INDEX IF NOT EXISTS idx_travel_guides_title_gin ON travel_guides USING gin(to_tsvector('chinese', title));
CREATE INDEX IF NOT EXISTS idx_travel_guides_content_gin ON travel_guides USING gin(to_tsvector('chinese', content));

-- 创建触发器函数用于更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_travel_plans_updated_at BEFORE UPDATE ON travel_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_travel_guides_updated_at BEFORE UPDATE ON travel_guides
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_plan_reviews_updated_at BEFORE UPDATE ON plan_reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入示例数据
INSERT INTO users (username, email, password_hash, full_name, preferences) VALUES
('demo_user', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L3jzjvG4e', '演示用户', '{"preferred_travel_type": "文化", "budget_range": "中等"}'::jsonb)
ON CONFLICT (username) DO NOTHING;

-- 提交事务
COMMIT;
