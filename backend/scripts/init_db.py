#!/usr/bin/env python3
"""
数据库初始化脚本
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text
from app.core.config import settings
from app.core.database import Base
from app.models.travel import TravelPlan, User, TravelGuide, WeatherData, PlanReview, SearchLog
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_database_if_not_exists():
    """创建数据库（如果不存在）"""
    try:
        # 解析数据库URL
        db_url_parts = settings.DATABASE_URL.split('/')
        db_name = db_url_parts[-1]
        base_url = '/'.join(db_url_parts[:-1])
        
        # 连接到PostgreSQL服务器（不指定数据库）
        engine = create_engine(f"{base_url}/postgres")
        
        with engine.connect() as conn:
            # 检查数据库是否存在
            result = conn.execute(
                text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
                {"db_name": db_name}
            )
            
            if not result.fetchone():
                # 创建数据库
                conn.execute(text("COMMIT"))  # 结束当前事务
                conn.execute(text(f"CREATE DATABASE {db_name}"))
                logger.info(f"数据库 {db_name} 创建成功")
            else:
                logger.info(f"数据库 {db_name} 已存在")
        
        engine.dispose()
        
    except Exception as e:
        logger.error(f"创建数据库失败: {str(e)}")
        raise


def create_tables():
    """创建数据表"""
    try:
        engine = create_engine(settings.DATABASE_URL)
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("数据表创建成功")
        
        engine.dispose()
        
    except Exception as e:
        logger.error(f"创建数据表失败: {str(e)}")
        raise


def create_indexes():
    """创建额外的索引"""
    try:
        engine = create_engine(settings.DATABASE_URL)
        
        with engine.connect() as conn:
            # 创建复合索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_travel_plans_destination_duration ON travel_plans(destination, duration);",
                "CREATE INDEX IF NOT EXISTS idx_travel_plans_created_at ON travel_plans(created_at DESC);",
                "CREATE INDEX IF NOT EXISTS idx_travel_guides_location_platform ON travel_guides(location, platform);",
                "CREATE INDEX IF NOT EXISTS idx_weather_data_location_time ON weather_data(location, observation_time DESC);",
                "CREATE INDEX IF NOT EXISTS idx_plan_reviews_plan_rating ON plan_reviews(plan_id, rating DESC);",
                "CREATE INDEX IF NOT EXISTS idx_search_logs_query_time ON search_logs(query, created_at DESC);"
            ]
            
            for index_sql in indexes:
                try:
                    conn.execute(text(index_sql))
                    logger.info(f"索引创建成功: {index_sql.split()[5]}")
                except Exception as e:
                    logger.warning(f"索引创建失败: {str(e)}")
            
            conn.commit()
        
        engine.dispose()
        
    except Exception as e:
        logger.error(f"创建索引失败: {str(e)}")


def insert_sample_data():
    """插入示例数据"""
    try:
        engine = create_engine(settings.DATABASE_URL)
        
        with engine.connect() as conn:
            # 插入示例用户
            sample_user_sql = """
            INSERT INTO users (id, username, email, password_hash, full_name, preferences)
            VALUES (
                gen_random_uuid(),
                'demo_user',
                '<EMAIL>',
                '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L3jzjvG4e',
                '演示用户',
                '{"preferred_travel_type": "文化", "budget_range": "中等"}'::jsonb
            )
            ON CONFLICT (username) DO NOTHING;
            """
            
            conn.execute(text(sample_user_sql))
            
            # 插入示例旅游计划
            sample_plan_sql = """
            INSERT INTO travel_plans (
                id, destination, duration, budget, travel_type, group_size,
                plan_content, status
            )
            VALUES (
                gen_random_uuid(),
                '北京',
                3,
                3000.0,
                '文化',
                2,
                '{"overview": "北京3日文化之旅", "daily_itinerary": []}'::jsonb,
                'draft'
            )
            ON CONFLICT DO NOTHING;
            """
            
            conn.execute(text(sample_plan_sql))
            
            conn.commit()
            logger.info("示例数据插入成功")
        
        engine.dispose()
        
    except Exception as e:
        logger.error(f"插入示例数据失败: {str(e)}")


def main():
    """主函数"""
    try:
        logger.info("开始初始化数据库...")
        
        # 1. 创建数据库
        create_database_if_not_exists()
        
        # 2. 创建数据表
        create_tables()
        
        # 3. 创建索引
        create_indexes()
        
        # 4. 插入示例数据
        insert_sample_data()
        
        logger.info("数据库初始化完成！")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
