"""
LangChain服务 - AI模型集成和管理
"""

from typing import Optional, Dict, Any, List
from langchain.llms import OpenAI
from langchain.chat_models import ChatOpenAI
from langchain.prompts import PromptTemplate, ChatPromptTemplate
from langchain.chains import LL<PERSON>hain
from langchain.memory import ConversationBufferMemory
from langchain.schema import BaseMessage, HumanMessage, SystemMessage
import logging

from ..core.config import settings

logger = logging.getLogger(__name__)


class LangChainService:
    """LangChain服务类"""
    
    def __init__(self):
        self.llm = None
        self.chat_model = None
        self.memory = ConversationBufferMemory()
        self._initialize_models()
    
    def _initialize_models(self):
        """初始化AI模型"""
        try:
            if settings.OPENAI_API_KEY:
                self.chat_model = ChatOpenAI(
                    openai_api_key=settings.OPENAI_API_KEY,
                    model_name=settings.DEFAULT_MODEL,
                    temperature=0.7,
                    max_tokens=2000
                )
                logger.info("OpenAI模型初始化成功")
            else:
                logger.warning("未配置OpenAI API密钥")
                
        except Exception as e:
            logger.error(f"模型初始化失败: {str(e)}")
    
    def get_travel_planning_prompt(self) -> ChatPromptTemplate:
        """获取旅游规划提示词模板"""
        system_message = """你是一个专业的旅游规划师。根据用户提供的信息，为他们制定详细的旅游计划。

请根据以下信息制定旅游计划：
- 目的地：{destination}
- 旅游天数：{duration}天
- 预算：{budget}元（如果提供）
- 旅游类型：{travel_type}
- 出行人数：{group_size}人
- 特殊需求：{special_requirements}

请提供包含以下内容的详细计划：
1. 每日行程安排（具体到时间段）
2. 推荐景点和活动（包括门票价格）
3. 住宿建议（不同价位选择）
4. 交通方案（往返和当地交通）
5. 餐饮推荐（特色美食和餐厅）
6. 购物建议
7. 注意事项和小贴士
8. 预算分配建议

请确保计划实用、详细且符合预算要求。"""

        human_message = """请为我制定一个{destination}的{duration}天旅游计划。"""
        
        return ChatPromptTemplate.from_messages([
            SystemMessage(content=system_message),
            HumanMessage(content=human_message)
        ])
    
    def get_weather_analysis_prompt(self) -> PromptTemplate:
        """获取天气分析提示词模板"""
        template = """根据以下天气信息，分析对旅游计划的影响并提供建议：

天气信息：
{weather_data}

旅游计划：
{travel_plan}

请分析：
1. 天气对各项活动的影响
2. 需要调整的行程安排
3. 穿衣和装备建议
4. 替代活动方案

分析结果："""
        
        return PromptTemplate(
            input_variables=["weather_data", "travel_plan"],
            template=template
        )
    
    def get_guide_summary_prompt(self) -> PromptTemplate:
        """获取攻略汇总提示词模板"""
        template = """请分析以下旅游攻略内容，提取关键信息并生成摘要：

攻略内容：
{guide_content}

目的地：{destination}

请提取并整理：
1. 推荐景点和活动
2. 美食推荐
3. 住宿建议
4. 交通信息
5. 实用小贴士
6. 费用参考
7. 最佳游览时间

摘要："""
        
        return PromptTemplate(
            input_variables=["guide_content", "destination"],
            template=template
        )
    
    async def generate_travel_plan(
        self,
        destination: str,
        duration: int,
        budget: Optional[float] = None,
        travel_type: str = "休闲",
        group_size: int = 1,
        special_requirements: str = "无"
    ) -> Dict[str, Any]:
        """生成旅游计划"""
        try:
            if not self.chat_model:
                raise ValueError("AI模型未初始化")
            
            prompt = self.get_travel_planning_prompt()
            
            # 格式化输入
            formatted_prompt = prompt.format_messages(
                destination=destination,
                duration=duration,
                budget=f"{budget}" if budget else "未指定",
                travel_type=travel_type,
                group_size=group_size,
                special_requirements=special_requirements
            )
            
            # 生成回复
            response = await self.chat_model.agenerate([formatted_prompt])
            plan_content = response.generations[0][0].text
            
            return {
                "success": True,
                "plan": plan_content,
                "metadata": {
                    "destination": destination,
                    "duration": duration,
                    "budget": budget,
                    "travel_type": travel_type,
                    "group_size": group_size
                }
            }
            
        except Exception as e:
            logger.error(f"生成旅游计划失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "plan": None
            }
    
    async def analyze_weather_impact(
        self,
        weather_data: Dict[str, Any],
        travel_plan: str
    ) -> Dict[str, Any]:
        """分析天气对旅游计划的影响"""
        try:
            if not self.chat_model:
                raise ValueError("AI模型未初始化")
            
            prompt = self.get_weather_analysis_prompt()
            chain = LLMChain(llm=self.chat_model, prompt=prompt)
            
            result = await chain.arun(
                weather_data=str(weather_data),
                travel_plan=travel_plan
            )
            
            return {
                "success": True,
                "analysis": result
            }
            
        except Exception as e:
            logger.error(f"天气影响分析失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def summarize_guides(
        self,
        guide_content: str,
        destination: str
    ) -> Dict[str, Any]:
        """汇总攻略内容"""
        try:
            if not self.chat_model:
                raise ValueError("AI模型未初始化")
            
            prompt = self.get_guide_summary_prompt()
            chain = LLMChain(llm=self.chat_model, prompt=prompt)
            
            result = await chain.arun(
                guide_content=guide_content,
                destination=destination
            )
            
            return {
                "success": True,
                "summary": result
            }
            
        except Exception as e:
            logger.error(f"攻略汇总失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }


# 全局LangChain服务实例
langchain_service = LangChainService()


def get_langchain_service() -> LangChainService:
    """获取LangChain服务实例"""
    return langchain_service
