"""
旅游规划服务 - 核心业务逻辑
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import json
import logging
from sqlalchemy.orm import Session

from .langchain_service import get_langchain_service
from ..core.database import get_redis
# 注意：这里暂时注释掉模型导入，避免循环导入
# from ..models.travel import TravelPlan
# from ..schemas.travel import TravelPlanCreate, TravelPlanResponse

logger = logging.getLogger(__name__)


class TravelPlanningService:
    """旅游规划服务类"""
    
    def __init__(self):
        self.langchain_service = get_langchain_service()
        self.redis_client = get_redis()
    
    async def create_travel_plan(
        self,
        plan_data: Dict[str, Any],  # 暂时使用Dict替代TravelPlanCreate
        user_id: Optional[str] = None,
        db: Optional[Session] = None
    ) -> Dict[str, Any]:
        """创建旅游计划"""
        try:
            # 生成AI旅游计划
            ai_result = await self.langchain_service.generate_travel_plan(
                destination=plan_data.get("destination"),
                duration=plan_data.get("duration"),
                budget=plan_data.get("budget"),
                travel_type=plan_data.get("travel_type", "休闲"),
                group_size=plan_data.get("group_size", 1),
                special_requirements=plan_data.get("special_requirements") or "无"
            )
            
            if not ai_result["success"]:
                return {
                    "success": False,
                    "error": "AI生成旅游计划失败",
                    "detail": ai_result.get("error")
                }
            
            # 解析和结构化AI生成的计划
            structured_plan = self._parse_ai_plan(ai_result["plan"])
            
            # 保存到数据库（如果提供了数据库会话）
            travel_plan = None
            if db and user_id:
                # 暂时注释掉数据库保存，避免导入问题
                # travel_plan = TravelPlan(...)
                pass
            
            # 缓存结果
            cache_key = f"travel_plan:{plan_data.get('destination')}:{plan_data.get('duration')}:{hash(str(plan_data))}"
            await self._cache_plan(cache_key, structured_plan)
            
            return {
                "success": True,
                "plan_id": travel_plan.id if travel_plan else None,
                "plan": structured_plan,
                "raw_response": ai_result["plan"],
                "metadata": ai_result["metadata"]
            }
            
        except Exception as e:
            logger.error(f"创建旅游计划失败: {str(e)}")
            return {
                "success": False,
                "error": "创建旅游计划失败",
                "detail": str(e)
            }
    
    def _parse_ai_plan(self, ai_response: str) -> Dict[str, Any]:
        """解析AI生成的旅游计划"""
        try:
            # 基础结构化数据
            structured_plan = {
                "overview": "",
                "daily_itinerary": [],
                "accommodations": [],
                "transportation": {},
                "dining": [],
                "shopping": [],
                "tips": [],
                "budget_breakdown": {},
                "generated_at": datetime.now().isoformat()
            }
            
            # 简单的文本解析（实际项目中可以使用更复杂的NLP解析）
            lines = ai_response.split('\n')
            current_section = "overview"
            current_day = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 识别不同部分
                if "行程安排" in line or "每日安排" in line:
                    current_section = "itinerary"
                elif "住宿" in line:
                    current_section = "accommodation"
                elif "交通" in line:
                    current_section = "transportation"
                elif "餐饮" in line or "美食" in line:
                    current_section = "dining"
                elif "购物" in line:
                    current_section = "shopping"
                elif "注意事项" in line or "小贴士" in line:
                    current_section = "tips"
                elif "预算" in line:
                    current_section = "budget"
                elif line.startswith("第") and "天" in line:
                    # 新的一天
                    current_day = {
                        "day": len(structured_plan["daily_itinerary"]) + 1,
                        "title": line,
                        "activities": [],
                        "meals": [],
                        "notes": ""
                    }
                    structured_plan["daily_itinerary"].append(current_day)
                else:
                    # 添加内容到相应部分
                    if current_section == "overview" and not structured_plan["overview"]:
                        structured_plan["overview"] = line
                    elif current_section == "itinerary" and current_day:
                        if ":" in line or "：" in line:
                            current_day["activities"].append(line)
                    elif current_section == "accommodation":
                        structured_plan["accommodations"].append(line)
                    elif current_section == "dining":
                        structured_plan["dining"].append(line)
                    elif current_section == "shopping":
                        structured_plan["shopping"].append(line)
                    elif current_section == "tips":
                        structured_plan["tips"].append(line)
            
            return structured_plan
            
        except Exception as e:
            logger.error(f"解析AI计划失败: {str(e)}")
            # 返回原始响应作为备选
            return {
                "raw_content": ai_response,
                "parsed": False,
                "generated_at": datetime.now().isoformat()
            }
    
    async def _cache_plan(self, cache_key: str, plan_data: Dict[str, Any], ttl: int = 3600):
        """缓存旅游计划"""
        try:
            await self.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(plan_data, ensure_ascii=False, default=str)
            )
        except Exception as e:
            logger.error(f"缓存计划失败: {str(e)}")
    
    async def get_cached_plan(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存的计划"""
        try:
            cached_data = await self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"获取缓存计划失败: {str(e)}")
        return None
    
    async def optimize_plan_with_weather(
        self,
        plan: Dict[str, Any],
        weather_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """根据天气优化旅游计划"""
        try:
            # 使用LangChain分析天气影响
            analysis_result = await self.langchain_service.analyze_weather_impact(
                weather_data=weather_data,
                travel_plan=json.dumps(plan, ensure_ascii=False)
            )
            
            if analysis_result["success"]:
                # 将天气分析结果添加到计划中
                plan["weather_analysis"] = analysis_result["analysis"]
                plan["weather_data"] = weather_data
                plan["optimized_at"] = datetime.now().isoformat()
            
            return plan
            
        except Exception as e:
            logger.error(f"天气优化失败: {str(e)}")
            return plan
    
    async def get_plan_recommendations(
        self,
        destination: str,
        preferences: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """获取目的地推荐"""
        try:
            # 基于目的地和偏好生成推荐
            recommendations = []
            
            # 这里可以集成更多的推荐逻辑
            # 例如：热门景点、季节性活动、用户评价等
            
            return recommendations
            
        except Exception as e:
            logger.error(f"获取推荐失败: {str(e)}")
            return []
    
    def validate_plan_data(self, plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证旅游计划数据"""
        errors = []
        
        # 基础验证
        if not plan_data.get("destination"):
            errors.append("目的地不能为空")

        duration = plan_data.get("duration", 0)
        if duration <= 0:
            errors.append("旅游天数必须大于0")

        if duration > 30:
            errors.append("旅游天数不能超过30天")

        budget = plan_data.get("budget")
        if budget and budget < 0:
            errors.append("预算不能为负数")

        group_size = plan_data.get("group_size", 1)
        if group_size <= 0:
            errors.append("出行人数必须大于0")

        if group_size > 20:
            errors.append("出行人数不能超过20人")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }


# 全局旅游规划服务实例
travel_service = TravelPlanningService()


def get_travel_service() -> TravelPlanningService:
    """获取旅游规划服务实例"""
    return travel_service
