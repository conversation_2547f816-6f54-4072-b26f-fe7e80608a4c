"""
AI模型配置和管理
"""

from typing import Dict, Any, Optional
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ModelProvider(str, Enum):
    """AI模型提供商"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    LOCAL = "local"


class ModelConfig:
    """模型配置类"""
    
    # OpenAI模型配置
    OPENAI_MODELS = {
        "gpt-3.5-turbo": {
            "max_tokens": 4096,
            "temperature": 0.7,
            "cost_per_1k_tokens": 0.002
        },
        "gpt-4": {
            "max_tokens": 8192,
            "temperature": 0.7,
            "cost_per_1k_tokens": 0.03
        },
        "gpt-4-turbo": {
            "max_tokens": 128000,
            "temperature": 0.7,
            "cost_per_1k_tokens": 0.01
        }
    }
    
    # Anthropic模型配置
    ANTHROPIC_MODELS = {
        "claude-3-sonnet": {
            "max_tokens": 200000,
            "temperature": 0.7,
            "cost_per_1k_tokens": 0.003
        },
        "claude-3-opus": {
            "max_tokens": 200000,
            "temperature": 0.7,
            "cost_per_1k_tokens": 0.015
        }
    }
    
    # 提示词模板配置
    PROMPT_TEMPLATES = {
        "travel_planning": {
            "system_role": "专业旅游规划师",
            "max_length": 2000,
            "language": "中文"
        },
        "weather_analysis": {
            "system_role": "天气分析专家",
            "max_length": 1000,
            "language": "中文"
        },
        "guide_summary": {
            "system_role": "旅游内容分析师",
            "max_length": 1500,
            "language": "中文"
        }
    }
    
    @classmethod
    def get_model_config(cls, provider: str, model_name: str) -> Optional[Dict[str, Any]]:
        """获取模型配置"""
        if provider == ModelProvider.OPENAI:
            return cls.OPENAI_MODELS.get(model_name)
        elif provider == ModelProvider.ANTHROPIC:
            return cls.ANTHROPIC_MODELS.get(model_name)
        return None
    
    @classmethod
    def get_available_models(cls, provider: str) -> list:
        """获取可用模型列表"""
        if provider == ModelProvider.OPENAI:
            return list(cls.OPENAI_MODELS.keys())
        elif provider == ModelProvider.ANTHROPIC:
            return list(cls.ANTHROPIC_MODELS.keys())
        return []
    
    @classmethod
    def estimate_cost(cls, provider: str, model_name: str, token_count: int) -> float:
        """估算API调用成本"""
        config = cls.get_model_config(provider, model_name)
        if config:
            return (token_count / 1000) * config["cost_per_1k_tokens"]
        return 0.0


class PromptManager:
    """提示词管理器"""
    
    def __init__(self):
        self.templates = {}
        self._load_templates()
    
    def _load_templates(self):
        """加载提示词模板"""
        # 旅游规划模板
        self.templates["travel_planning"] = {
            "system": """你是一个专业的旅游规划师，拥有丰富的旅游经验和深度的目的地知识。
请根据用户提供的信息，制定详细、实用、个性化的旅游计划。

要求：
1. 计划要具体到时间段和具体活动
2. 考虑交通便利性和时间安排的合理性
3. 提供不同价位的选择
4. 包含实用的小贴士和注意事项
5. 确保安全性和可行性""",
            
            "human": """请为我制定一个详细的旅游计划：
目的地：{destination}
旅游天数：{duration}天
预算：{budget}
旅游类型：{travel_type}
出行人数：{group_size}人
特殊需求：{special_requirements}

请提供完整的行程规划。"""
        }
        
        # 天气分析模板
        self.templates["weather_analysis"] = {
            "system": """你是一个专业的天气分析师和旅游顾问。
请根据天气预报信息，分析对旅游行程的影响，并提供专业的调整建议。""",
            
            "human": """天气信息：{weather_data}
原定行程：{travel_plan}

请分析天气对行程的影响并提供调整建议。"""
        }
        
        # 攻略汇总模板
        self.templates["guide_summary"] = {
            "system": """你是一个专业的旅游内容分析师。
请从提供的攻略内容中提取关键信息，生成结构化的摘要。""",
            
            "human": """目的地：{destination}
攻略内容：{guide_content}

请提取关键信息并生成摘要。"""
        }
    
    def get_template(self, template_name: str) -> Optional[Dict[str, str]]:
        """获取提示词模板"""
        return self.templates.get(template_name)
    
    def format_prompt(self, template_name: str, **kwargs) -> Optional[Dict[str, str]]:
        """格式化提示词"""
        template = self.get_template(template_name)
        if not template:
            return None
        
        try:
            return {
                "system": template["system"],
                "human": template["human"].format(**kwargs)
            }
        except KeyError as e:
            logger.error(f"提示词格式化失败，缺少参数: {e}")
            return None


# 全局提示词管理器实例
prompt_manager = PromptManager()


def get_prompt_manager() -> PromptManager:
    """获取提示词管理器实例"""
    return prompt_manager
