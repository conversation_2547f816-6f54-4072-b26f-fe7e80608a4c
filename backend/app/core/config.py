"""
应用配置管理
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    APP_NAME: str = "OpenTravel API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    SECRET_KEY: str = "your-secret-key-change-in-production"
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://opentravel_user:opentravel_pass@localhost:5432/opentravel"
    REDIS_URL: str = "redis://localhost:6379"
    
    # AI模型配置
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    DEFAULT_MODEL: str = "gpt-3.5-turbo"
    
    # 天气API配置
    OPENWEATHER_API_KEY: Optional[str] = None
    
    # JWT配置
    JWT_SECRET_KEY: str = "jwt-secret-key"
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_MINUTES: int = 30
    
    # CORS配置
    CORS_ORIGINS: list = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # 文件上传配置
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    UPLOAD_DIR: str = "uploads"
    
    # 缓存配置
    CACHE_TTL: int = 3600  # 1小时
    WEATHER_CACHE_TTL: int = 1800  # 30分钟
    GUIDE_CACHE_TTL: int = 86400  # 24小时
    
    # API限流配置
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_PER_HOUR: int = 1000
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    # MCP工具配置
    MCP_WEATHER_TIMEOUT: int = 30
    MCP_SCRAPER_TIMEOUT: int = 60
    MCP_SCRAPER_DELAY: int = 1
    
    # 爬虫配置
    SCRAPER_USER_AGENT: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    SCRAPER_MAX_PAGES: int = 10
    SCRAPER_CONCURRENT_REQUESTS: int = 5
    
    class Config:
        env_file = "config/.env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
