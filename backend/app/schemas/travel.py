"""
旅游相关的Pydantic模型
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum


class TravelType(str, Enum):
    """旅游类型枚举"""
    LEISURE = "休闲"
    ADVENTURE = "探险"
    CULTURE = "文化"
    FOOD = "美食"
    BUSINESS = "商务"
    FAMILY = "家庭"
    ROMANTIC = "浪漫"
    PHOTOGRAPHY = "摄影"


class PlanStatus(str, Enum):
    """计划状态枚举"""
    DRAFT = "draft"
    CONFIRMED = "confirmed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TravelPlanCreate(BaseModel):
    """创建旅游计划的请求模型"""
    destination: str = Field(..., min_length=1, max_length=100, description="目的地")
    duration: int = Field(..., ge=1, le=30, description="旅游天数")
    budget: Optional[float] = Field(None, ge=0, description="预算（元）")
    travel_type: TravelType = Field(TravelType.LEISURE, description="旅游类型")
    group_size: int = Field(1, ge=1, le=20, description="出行人数")
    special_requirements: Optional[str] = Field(None, max_length=500, description="特殊需求")
    start_date: Optional[datetime] = Field(None, description="出发日期")
    
    @validator('destination')
    def validate_destination(cls, v):
        if not v or not v.strip():
            raise ValueError('目的地不能为空')
        return v.strip()
    
    @validator('special_requirements')
    def validate_special_requirements(cls, v):
        if v:
            return v.strip()
        return v


class TravelPlanUpdate(BaseModel):
    """更新旅游计划的请求模型"""
    destination: Optional[str] = Field(None, min_length=1, max_length=100)
    duration: Optional[int] = Field(None, ge=1, le=30)
    budget: Optional[float] = Field(None, ge=0)
    travel_type: Optional[TravelType] = None
    group_size: Optional[int] = Field(None, ge=1, le=20)
    special_requirements: Optional[str] = Field(None, max_length=500)
    status: Optional[PlanStatus] = None


class DailyItinerary(BaseModel):
    """每日行程模型"""
    day: int = Field(..., description="第几天")
    title: str = Field(..., description="当日标题")
    activities: List[str] = Field(default_factory=list, description="活动列表")
    meals: List[str] = Field(default_factory=list, description="餐饮安排")
    notes: str = Field("", description="备注")


class TravelPlanContent(BaseModel):
    """旅游计划内容模型"""
    overview: str = Field("", description="行程概述")
    daily_itinerary: List[DailyItinerary] = Field(default_factory=list, description="每日行程")
    accommodations: List[str] = Field(default_factory=list, description="住宿建议")
    transportation: Dict[str, Any] = Field(default_factory=dict, description="交通信息")
    dining: List[str] = Field(default_factory=list, description="餐饮推荐")
    shopping: List[str] = Field(default_factory=list, description="购物建议")
    tips: List[str] = Field(default_factory=list, description="小贴士")
    budget_breakdown: Dict[str, Any] = Field(default_factory=dict, description="预算分解")
    weather_analysis: Optional[str] = Field(None, description="天气分析")
    generated_at: datetime = Field(default_factory=datetime.now, description="生成时间")


class TravelPlanResponse(BaseModel):
    """旅游计划响应模型"""
    id: Optional[str] = None
    user_id: Optional[str] = None
    destination: str
    duration: int
    budget: Optional[float] = None
    travel_type: TravelType
    group_size: int
    special_requirements: Optional[str] = None
    start_date: Optional[datetime] = None
    plan_content: TravelPlanContent
    status: PlanStatus = PlanStatus.DRAFT
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class TravelPlanList(BaseModel):
    """旅游计划列表响应模型"""
    plans: List[TravelPlanResponse]
    total: int
    page: int
    size: int


class WeatherInfo(BaseModel):
    """天气信息模型"""
    location: str
    current_temp: float
    description: str
    humidity: int
    wind_speed: float
    forecast: List[Dict[str, Any]] = Field(default_factory=list)


class TravelRecommendation(BaseModel):
    """旅游推荐模型"""
    destination: str
    title: str
    description: str
    rating: float
    price_range: str
    best_time: str
    duration_suggestion: str
    highlights: List[str] = Field(default_factory=list)


class PlanGenerationRequest(BaseModel):
    """计划生成请求模型"""
    destination: str
    duration: int
    budget: Optional[float] = None
    travel_type: TravelType = TravelType.LEISURE
    group_size: int = 1
    special_requirements: Optional[str] = None
    include_weather: bool = True
    include_guides: bool = True


class PlanGenerationResponse(BaseModel):
    """计划生成响应模型"""
    success: bool
    plan_id: Optional[str] = None
    plan: TravelPlanContent
    raw_response: Optional[str] = None
    weather_info: Optional[WeatherInfo] = None
    recommendations: List[TravelRecommendation] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[str] = None
