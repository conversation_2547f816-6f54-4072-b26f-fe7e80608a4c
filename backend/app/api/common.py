"""
通用API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any
import logging

from ..core.database import get_db, get_redis
from ..core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1", tags=["common"])


@router.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "OpenTravel API",
        "version": settings.APP_VERSION,
        "environment": "development" if settings.DEBUG else "production"
    }


@router.get("/status")
async def get_system_status(db: Session = Depends(get_db)):
    """系统状态检查"""
    try:
        # 检查数据库连接
        db_status = "healthy"
        try:
            db.execute("SELECT 1")
        except Exception as e:
            db_status = f"error: {str(e)}"
        
        # 检查Redis连接
        redis_status = "healthy"
        try:
            redis_client = get_redis()
            redis_client.ping()
        except Exception as e:
            redis_status = f"error: {str(e)}"
        
        # 检查AI服务状态
        ai_status = "configured" if settings.OPENAI_API_KEY else "not_configured"
        
        return {
            "api_version": "v1",
            "status": "running",
            "components": {
                "database": db_status,
                "redis": redis_status,
                "ai_service": ai_status
            },
            "features": {
                "ai_planning": "available",
                "weather_integration": "development",
                "guide_aggregation": "development",
                "user_system": "development"
            }
        }
        
    except Exception as e:
        logger.error(f"系统状态检查错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="系统状态检查失败"
        )


@router.get("/config")
async def get_public_config():
    """获取公开配置信息"""
    return {
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "features": {
            "ai_models": ["gpt-3.5-turbo", "gpt-4"] if settings.OPENAI_API_KEY else [],
            "weather_sources": ["openweather"] if settings.OPENWEATHER_API_KEY else [],
            "supported_languages": ["zh-CN", "en-US"],
            "max_plan_duration": 30,
            "max_group_size": 20
        },
        "limits": {
            "max_file_size": settings.MAX_FILE_SIZE,
            "rate_limit_per_minute": settings.RATE_LIMIT_PER_MINUTE,
            "cache_ttl": settings.CACHE_TTL
        }
    }


@router.get("/statistics")
async def get_system_statistics(db: Session = Depends(get_db)):
    """获取系统统计信息"""
    try:
        from ..models.travel import TravelPlan, User, TravelGuide
        
        # 统计旅游计划
        total_plans = db.query(TravelPlan).count()
        public_plans = db.query(TravelPlan).filter(TravelPlan.is_public == True).count()
        
        # 统计用户
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.is_active == True).count()
        
        # 统计攻略
        total_guides = db.query(TravelGuide).count()
        
        # 热门目的地
        popular_destinations = db.query(
            TravelPlan.destination,
            db.func.count(TravelPlan.id).label('count')
        ).group_by(
            TravelPlan.destination
        ).order_by(
            db.func.count(TravelPlan.id).desc()
        ).limit(5).all()
        
        return {
            "plans": {
                "total": total_plans,
                "public": public_plans,
                "private": total_plans - public_plans
            },
            "users": {
                "total": total_users,
                "active": active_users
            },
            "guides": {
                "total": total_guides
            },
            "popular_destinations": [
                {"destination": dest.destination, "count": dest.count}
                for dest in popular_destinations
            ]
        }
        
    except Exception as e:
        logger.error(f"获取统计信息错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )


@router.post("/feedback")
async def submit_feedback(
    feedback_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """提交用户反馈"""
    try:
        # 这里可以将反馈保存到数据库或发送邮件
        logger.info(f"收到用户反馈: {feedback_data}")
        
        return {
            "message": "反馈提交成功",
            "feedback_id": "temp_id"  # 实际项目中应该生成真实的ID
        }
        
    except Exception as e:
        logger.error(f"提交反馈错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交反馈失败"
        )


@router.get("/version")
async def get_version_info():
    """获取版本信息"""
    return {
        "version": settings.APP_VERSION,
        "build_date": "2024-01-01",  # 实际项目中应该从构建信息获取
        "git_commit": "latest",      # 实际项目中应该从Git信息获取
        "api_version": "v1",
        "changelog_url": "https://github.com/makaixindalao/openTravel/releases"
    }
