"""
旅游相关API路由
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
import logging

from ..core.database import get_db
from ..services.travel_service import get_travel_service
from ..schemas.travel import (
    TravelPlanCreate, TravelPlanResponse, TravelPlanUpdate,
    PlanGenerationRequest, PlanGenerationResponse, TravelPlanList
)
from ..models.travel import TravelPlan

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/travel", tags=["travel"])


@router.post("/generate", response_model=PlanGenerationResponse)
async def generate_travel_plan(
    request: PlanGenerationRequest,
    db: Session = Depends(get_db)
):
    """生成旅游计划"""
    try:
        travel_service = get_travel_service()
        
        # 验证输入数据
        validation_result = travel_service.validate_plan_data(request)
        if not validation_result["valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"errors": validation_result["errors"]}
            )
        
        # 生成旅游计划
        result = await travel_service.create_travel_plan(
            plan_data=request,
            db=db
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "生成旅游计划失败")
            )
        
        return PlanGenerationResponse(
            success=True,
            plan_id=result.get("plan_id"),
            plan=result["plan"],
            raw_response=result.get("raw_response"),
            metadata=result.get("metadata", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成旅游计划API错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )


@router.get("/plans", response_model=TravelPlanList)
async def get_travel_plans(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    destination: Optional[str] = Query(None, description="目的地筛选"),
    travel_type: Optional[str] = Query(None, description="旅游类型筛选"),
    db: Session = Depends(get_db)
):
    """获取旅游计划列表"""
    try:
        # 构建查询
        query = db.query(TravelPlan)
        
        # 应用筛选条件
        if destination:
            query = query.filter(TravelPlan.destination.ilike(f"%{destination}%"))
        if travel_type:
            query = query.filter(TravelPlan.travel_type == travel_type)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        offset = (page - 1) * size
        plans = query.offset(offset).limit(size).all()
        
        # 转换为响应模型
        plan_responses = [
            TravelPlanResponse.from_orm(plan) for plan in plans
        ]
        
        return TravelPlanList(
            plans=plan_responses,
            total=total,
            page=page,
            size=size
        )
        
    except Exception as e:
        logger.error(f"获取旅游计划列表错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取计划列表失败"
        )


@router.get("/plans/{plan_id}", response_model=TravelPlanResponse)
async def get_travel_plan(
    plan_id: str,
    db: Session = Depends(get_db)
):
    """获取单个旅游计划"""
    try:
        plan = db.query(TravelPlan).filter(TravelPlan.id == plan_id).first()
        
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="旅游计划不存在"
            )
        
        # 增加查看次数
        plan.view_count += 1
        db.commit()
        
        return TravelPlanResponse.from_orm(plan)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取旅游计划错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取计划详情失败"
        )


@router.put("/plans/{plan_id}", response_model=TravelPlanResponse)
async def update_travel_plan(
    plan_id: str,
    plan_update: TravelPlanUpdate,
    db: Session = Depends(get_db)
):
    """更新旅游计划"""
    try:
        plan = db.query(TravelPlan).filter(TravelPlan.id == plan_id).first()
        
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="旅游计划不存在"
            )
        
        # 更新字段
        update_data = plan_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(plan, field, value)
        
        db.commit()
        db.refresh(plan)
        
        return TravelPlanResponse.from_orm(plan)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新旅游计划错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新计划失败"
        )


@router.delete("/plans/{plan_id}")
async def delete_travel_plan(
    plan_id: str,
    db: Session = Depends(get_db)
):
    """删除旅游计划"""
    try:
        plan = db.query(TravelPlan).filter(TravelPlan.id == plan_id).first()
        
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="旅游计划不存在"
            )
        
        db.delete(plan)
        db.commit()
        
        return {"message": "旅游计划删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除旅游计划错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除计划失败"
        )


@router.get("/destinations/popular")
async def get_popular_destinations(
    limit: int = Query(10, ge=1, le=50, description="返回数量"),
    db: Session = Depends(get_db)
):
    """获取热门目的地"""
    try:
        # 查询最受欢迎的目的地
        popular_destinations = db.query(
            TravelPlan.destination,
            db.func.count(TravelPlan.id).label('plan_count'),
            db.func.avg(TravelPlan.budget).label('avg_budget'),
            db.func.avg(TravelPlan.duration).label('avg_duration')
        ).group_by(
            TravelPlan.destination
        ).order_by(
            db.func.count(TravelPlan.id).desc()
        ).limit(limit).all()
        
        result = []
        for dest in popular_destinations:
            result.append({
                "destination": dest.destination,
                "plan_count": dest.plan_count,
                "avg_budget": float(dest.avg_budget) if dest.avg_budget else None,
                "avg_duration": float(dest.avg_duration) if dest.avg_duration else None
            })
        
        return {"destinations": result}
        
    except Exception as e:
        logger.error(f"获取热门目的地错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取热门目的地失败"
        )


@router.get("/search")
async def search_travel_plans(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """搜索旅游计划"""
    try:
        # 构建搜索查询
        query = db.query(TravelPlan).filter(
            db.or_(
                TravelPlan.destination.ilike(f"%{q}%"),
                TravelPlan.travel_type.ilike(f"%{q}%")
            )
        )
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        offset = (page - 1) * size
        plans = query.offset(offset).limit(size).all()
        
        # 转换为响应模型
        plan_responses = [
            TravelPlanResponse.from_orm(plan) for plan in plans
        ]
        
        return {
            "plans": plan_responses,
            "total": total,
            "page": page,
            "size": size,
            "query": q
        }
        
    except Exception as e:
        logger.error(f"搜索旅游计划错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="搜索失败"
        )
