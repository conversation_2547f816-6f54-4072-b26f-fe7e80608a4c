"""
OpenTravel AI旅游推荐系统 - 主应用入口
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from contextlib import asynccontextmanager
import logging
import time

from .core.config import settings
from .core.database import init_db, close_db
from .api import travel, common


# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("正在启动OpenTravel API...")
    await init_db()
    logger.info("数据库初始化完成")

    yield

    # 关闭时执行
    logger.info("正在关闭OpenTravel API...")
    await close_db()
    logger.info("数据库连接已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.APP_NAME,
    description="基于LangChain的AI旅游推荐系统API",
    version=settings.APP_VERSION,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time

    logger.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.4f}s"
    )

    return response


# 注册路由
app.include_router(common.router)
app.include_router(travel.router)


@app.get("/")
async def root():
    """根路径 - API欢迎信息"""
    return {
        "message": "欢迎使用OpenTravel AI旅游推荐系统",
        "version": settings.APP_VERSION,
        "docs": "/docs",
        "api_docs": "/api/v1/status"
    }


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "OpenTravel API"}


@app.get("/api/v1/status")
async def api_status():
    """API状态检查"""
    return {
        "api_version": "v1",
        "status": "running",
        "environment": "development" if settings.DEBUG else "production",
        "features": {
            "ai_planning": "开发中",
            "weather_integration": "开发中",
            "guide_aggregation": "开发中",
            "user_system": "开发中"
        }
    }


# 异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": "服务器内部错误",
            "detail": str(exc) if settings.DEBUG else "请联系管理员"
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
