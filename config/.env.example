# 数据库配置
DATABASE_URL=postgresql://opentravel_user:opentravel_pass@localhost:5432/opentravel
REDIS_URL=redis://localhost:6379

# AI模型配置
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 天气API配置
OPENWEATHER_API_KEY=your_openweather_api_key_here

# 应用配置
SECRET_KEY=your_super_secret_key_here_change_in_production
DEBUG=true
ENVIRONMENT=development

# JWT配置
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# 邮件配置 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_TLS=true

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads/

# 缓存配置
CACHE_TTL=3600  # 1小时
WEATHER_CACHE_TTL=1800  # 30分钟
GUIDE_CACHE_TTL=86400  # 24小时

# API限流配置
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 前端配置
REACT_APP_API_URL=http://localhost:8000
REACT_APP_ENV=development

# 第三方服务配置
GAODE_API_KEY=your_gaode_api_key_here
BAIDU_API_KEY=your_baidu_api_key_here

# MCP工具配置
MCP_WEATHER_TIMEOUT=30
MCP_SCRAPER_TIMEOUT=60
MCP_SCRAPER_DELAY=1

# 爬虫配置
SCRAPER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
SCRAPER_MAX_PAGES=10
SCRAPER_CONCURRENT_REQUESTS=5

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# 安全配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库连接池配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
