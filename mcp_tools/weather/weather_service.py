"""
天气服务MCP工具
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class WeatherMCP:
    """天气查询MCP工具"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "http://api.openweathermap.org/data/2.5"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_current_weather(self, location: str, units: str = "metric") -> Dict[str, Any]:
        """获取当前天气"""
        try:
            url = f"{self.base_url}/weather"
            params = {
                "q": location,
                "appid": self.api_key,
                "units": units,
                "lang": "zh_cn"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._format_current_weather(data)
                else:
                    error_data = await response.json()
                    raise Exception(f"API错误: {error_data.get('message', '未知错误')}")
                    
        except Exception as e:
            logger.error(f"获取当前天气失败: {str(e)}")
            return {"error": str(e)}
    
    async def get_forecast(self, location: str, days: int = 5, units: str = "metric") -> Dict[str, Any]:
        """获取天气预报"""
        try:
            url = f"{self.base_url}/forecast"
            params = {
                "q": location,
                "appid": self.api_key,
                "units": units,
                "lang": "zh_cn"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._format_forecast(data, days)
                else:
                    error_data = await response.json()
                    raise Exception(f"API错误: {error_data.get('message', '未知错误')}")
                    
        except Exception as e:
            logger.error(f"获取天气预报失败: {str(e)}")
            return {"error": str(e)}
    
    def _format_current_weather(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化当前天气数据"""
        return {
            "location": data["name"],
            "country": data["sys"]["country"],
            "temperature": data["main"]["temp"],
            "feels_like": data["main"]["feels_like"],
            "humidity": data["main"]["humidity"],
            "pressure": data["main"]["pressure"],
            "wind_speed": data["wind"]["speed"],
            "wind_direction": data["wind"].get("deg", 0),
            "visibility": data.get("visibility", 0) / 1000,  # 转换为公里
            "weather_main": data["weather"][0]["main"],
            "weather_description": data["weather"][0]["description"],
            "weather_icon": data["weather"][0]["icon"],
            "sunrise": datetime.fromtimestamp(data["sys"]["sunrise"]).isoformat(),
            "sunset": datetime.fromtimestamp(data["sys"]["sunset"]).isoformat(),
            "observation_time": datetime.fromtimestamp(data["dt"]).isoformat()
        }
    
    def _format_forecast(self, data: Dict[str, Any], days: int) -> Dict[str, Any]:
        """格式化天气预报数据"""
        forecasts = []
        current_date = None
        daily_data = {}
        
        for item in data["list"][:days * 8]:  # 每天8个时间点（3小时间隔）
            dt = datetime.fromtimestamp(item["dt"])
            date_str = dt.strftime("%Y-%m-%d")
            
            if date_str != current_date:
                if current_date and daily_data:
                    forecasts.append(daily_data)
                
                current_date = date_str
                daily_data = {
                    "date": date_str,
                    "temperature_min": item["main"]["temp"],
                    "temperature_max": item["main"]["temp"],
                    "weather_main": item["weather"][0]["main"],
                    "weather_description": item["weather"][0]["description"],
                    "weather_icon": item["weather"][0]["icon"],
                    "humidity": item["main"]["humidity"],
                    "wind_speed": item["wind"]["speed"],
                    "hourly": []
                }
            
            # 更新最高最低温度
            daily_data["temperature_min"] = min(daily_data["temperature_min"], item["main"]["temp"])
            daily_data["temperature_max"] = max(daily_data["temperature_max"], item["main"]["temp"])
            
            # 添加小时数据
            daily_data["hourly"].append({
                "time": dt.strftime("%H:%M"),
                "temperature": item["main"]["temp"],
                "weather_description": item["weather"][0]["description"],
                "weather_icon": item["weather"][0]["icon"]
            })
        
        # 添加最后一天的数据
        if daily_data:
            forecasts.append(daily_data)
        
        return {
            "location": data["city"]["name"],
            "country": data["city"]["country"],
            "forecasts": forecasts[:days]
        }
    
    async def analyze_weather_for_travel(self, location: str, start_date: str, duration: int) -> Dict[str, Any]:
        """分析旅游期间的天气情况"""
        try:
            # 获取当前天气和预报
            current_weather = await self.get_current_weather(location)
            forecast = await self.get_forecast(location, duration)
            
            if "error" in current_weather or "error" in forecast:
                return {"error": "获取天气数据失败"}
            
            # 分析天气趋势
            analysis = self._analyze_weather_trends(current_weather, forecast)
            
            return {
                "current_weather": current_weather,
                "forecast": forecast,
                "analysis": analysis,
                "recommendations": self._generate_weather_recommendations(analysis)
            }
            
        except Exception as e:
            logger.error(f"天气分析失败: {str(e)}")
            return {"error": str(e)}
    
    def _analyze_weather_trends(self, current: Dict[str, Any], forecast: Dict[str, Any]) -> Dict[str, Any]:
        """分析天气趋势"""
        forecasts = forecast.get("forecasts", [])
        
        if not forecasts:
            return {"error": "无预报数据"}
        
        # 计算平均温度
        avg_temp = sum(day["temperature_max"] + day["temperature_min"] for day in forecasts) / (2 * len(forecasts))
        
        # 统计天气类型
        weather_types = {}
        for day in forecasts:
            weather = day["weather_main"]
            weather_types[weather] = weather_types.get(weather, 0) + 1
        
        # 找出主要天气类型
        dominant_weather = max(weather_types, key=weather_types.get)
        
        # 计算温度变化
        temp_range = {
            "min": min(day["temperature_min"] for day in forecasts),
            "max": max(day["temperature_max"] for day in forecasts)
        }
        
        return {
            "average_temperature": round(avg_temp, 1),
            "temperature_range": temp_range,
            "dominant_weather": dominant_weather,
            "weather_distribution": weather_types,
            "rainy_days": weather_types.get("Rain", 0),
            "sunny_days": weather_types.get("Clear", 0),
            "cloudy_days": weather_types.get("Clouds", 0)
        }
    
    def _generate_weather_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """生成天气相关的旅游建议"""
        recommendations = []
        
        avg_temp = analysis.get("average_temperature", 0)
        dominant_weather = analysis.get("dominant_weather", "")
        rainy_days = analysis.get("rainy_days", 0)
        
        # 温度建议
        if avg_temp < 10:
            recommendations.append("天气较冷，建议携带厚外套和保暖衣物")
        elif avg_temp > 30:
            recommendations.append("天气炎热，建议携带防晒用品和轻薄衣物")
        else:
            recommendations.append("温度适宜，建议携带轻便外套以备温差变化")
        
        # 天气类型建议
        if dominant_weather == "Rain":
            recommendations.append("预计多雨天气，建议携带雨具并安排室内活动")
        elif dominant_weather == "Clear":
            recommendations.append("天气晴朗，适合户外活动和观光")
        elif dominant_weather == "Clouds":
            recommendations.append("多云天气，适合拍照，光线柔和")
        
        # 雨天建议
        if rainy_days > 0:
            recommendations.append(f"预计有{rainy_days}天降雨，建议准备雨伞和防水装备")
        
        return recommendations


# 全局天气服务实例
weather_service = None


async def get_weather_service(api_key: str) -> WeatherMCP:
    """获取天气服务实例"""
    global weather_service
    if not weather_service:
        weather_service = WeatherMCP(api_key)
    return weather_service
